package com.ml.tomatoscan.analysis;

/**
 * Hybrid disease analysis system that automatically chooses between
 * offline TensorFlow Lite model and online Gemini API based on connectivity
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 02\u00020\u0001:\u00010B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J*\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\b2\b\b\u0002\u0010\u0012\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J\u0006\u0010\u0015\u001a\u00020\u0016J\u0018\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u0018\u0010\u001c\u001a\u00020\u00132\u0006\u0010\u0011\u001a\u00020\b2\u0006\u0010\u0012\u001a\u00020\u0013H\u0002J\u0006\u0010\u001d\u001a\u00020\u001eJ\u001e\u0010\u001f\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020!2\u0006\u0010\u000f\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010\"J\u000e\u0010#\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010$J\u0006\u0010%\u001a\u00020\bJ\u0006\u0010&\u001a\u00020\bJ\u0016\u0010\'\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010(J\u0016\u0010)\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010(J\u0016\u0010*\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010(J\u001c\u0010+\u001a\b\u0012\u0004\u0012\u00020-0,2\u0006\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010(J\u0010\u0010.\u001a\u00020!2\u0006\u0010/\u001a\u00020!H\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/ml/tomatoscan/analysis/HybridDiseaseAnalyzer;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "geminiApi", "Lcom/ml/tomatoscan/data/GeminiApi;", "isOfflineModelReady", "", "networkManager", "Lcom/ml/tomatoscan/network/NetworkConnectivityManager;", "offlineClassifier", "Lcom/ml/tomatoscan/ml/OfflineDiseaseClassifier;", "analyzeImage", "Lcom/ml/tomatoscan/analysis/AnalysisResult;", "bitmap", "Landroid/graphics/Bitmap;", "forceOffline", "userPreference", "Lcom/ml/tomatoscan/analysis/AnalysisMode;", "(Landroid/graphics/Bitmap;ZLcom/ml/tomatoscan/analysis/AnalysisMode;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cleanup", "", "convertTomatoAnalysisResult", "tomatoResult", "Lcom/ml/tomatoscan/data/TomatoAnalysisResult;", "processingTime", "", "determineAnalysisMode", "getNetworkStatus", "Lcom/ml/tomatoscan/analysis/NetworkStatus;", "handleOnlineFailureWithFallback", "originalError", "", "(Ljava/lang/String;Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "initialize", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isOfflineAvailable", "isOnlineAvailable", "performAutoAnalysis", "(Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "performOfflineAnalysis", "performOnlineAnalysis", "testFallbackMechanism", "", "Lcom/ml/tomatoscan/analysis/TestResult;", "validateAndCleanDiseaseName", "diseaseName", "Companion", "app_debug"})
public final class HybridDiseaseAnalyzer {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "HybridDiseaseAnalyzer";
    private static final long OFFLINE_TIMEOUT_MS = 3000L;
    private static final long ONLINE_TIMEOUT_MS = 10000L;
    private static final float AGRICULTURAL_CONFIDENCE_THRESHOLD = 0.5F;
    private static final float HIGH_CONFIDENCE_THRESHOLD = 0.75F;
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.network.NetworkConnectivityManager networkManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.ml.OfflineDiseaseClassifier offlineClassifier = null;
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.data.GeminiApi geminiApi = null;
    private boolean isOfflineModelReady = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.ml.tomatoscan.analysis.HybridDiseaseAnalyzer.Companion Companion = null;
    
    public HybridDiseaseAnalyzer(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Initialize the hybrid analyzer
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object initialize(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Analyze image using the best available method
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object analyzeImage(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, boolean forceOffline, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.analysis.AnalysisMode userPreference, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.ml.tomatoscan.analysis.AnalysisResult> $completion) {
        return null;
    }
    
    /**
     * Determine which analysis mode to use
     */
    private final com.ml.tomatoscan.analysis.AnalysisMode determineAnalysisMode(boolean forceOffline, com.ml.tomatoscan.analysis.AnalysisMode userPreference) {
        return null;
    }
    
    /**
     * Perform offline analysis using TensorFlow Lite
     */
    private final java.lang.Object performOfflineAnalysis(android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super com.ml.tomatoscan.analysis.AnalysisResult> $completion) {
        return null;
    }
    
    /**
     * Perform online analysis using Gemini API with enhanced fallback
     */
    private final java.lang.Object performOnlineAnalysis(android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super com.ml.tomatoscan.analysis.AnalysisResult> $completion) {
        return null;
    }
    
    /**
     * Validate and clean disease name to ensure proper mapping from model output
     */
    private final java.lang.String validateAndCleanDiseaseName(java.lang.String diseaseName) {
        return null;
    }
    
    /**
     * Handle online analysis failure with automatic offline fallback
     */
    private final java.lang.Object handleOnlineFailureWithFallback(java.lang.String originalError, android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super com.ml.tomatoscan.analysis.AnalysisResult> $completion) {
        return null;
    }
    
    /**
     * Convert TomatoAnalysisResult to AnalysisResult format with validation
     */
    private final com.ml.tomatoscan.analysis.AnalysisResult convertTomatoAnalysisResult(com.ml.tomatoscan.data.TomatoAnalysisResult tomatoResult, long processingTime) {
        return null;
    }
    
    /**
     * Perform automatic analysis with intelligent decision logic
     */
    private final java.lang.Object performAutoAnalysis(android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super com.ml.tomatoscan.analysis.AnalysisResult> $completion) {
        return null;
    }
    
    /**
     * Get current network status
     */
    @org.jetbrains.annotations.NotNull()
    public final com.ml.tomatoscan.analysis.NetworkStatus getNetworkStatus() {
        return null;
    }
    
    /**
     * Check if offline analysis is available
     */
    public final boolean isOfflineAvailable() {
        return false;
    }
    
    /**
     * Check if online analysis is available
     */
    public final boolean isOnlineAvailable() {
        return false;
    }
    
    /**
     * Test fallback mechanism with various network conditions
     * This method simulates different failure scenarios to validate fallback behavior
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object testFallbackMechanism(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.ml.tomatoscan.analysis.TestResult>> $completion) {
        return null;
    }
    
    /**
     * Clean up resources
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/ml/tomatoscan/analysis/HybridDiseaseAnalyzer$Companion;", "", "()V", "AGRICULTURAL_CONFIDENCE_THRESHOLD", "", "HIGH_CONFIDENCE_THRESHOLD", "OFFLINE_TIMEOUT_MS", "", "ONLINE_TIMEOUT_MS", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}