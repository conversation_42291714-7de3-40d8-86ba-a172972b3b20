package com.ml.tomatoscan.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000R\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a(\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\u0010\u0004\u001a\u0004\u0018\u00010\u00032\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a \u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0007\u001a \u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a\u001e\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a&\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00032\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00030\u00172\u0006\u0010\u0018\u001a\u00020\u0019H\u0007\u001a\b\u0010\u001a\u001a\u00020\u0001H\u0007\u001a\u0016\u0010\u001b\u001a\u00020\u00012\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001d0\u0017H\u0007\u001a\"\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00032\u0006\u0010\u001f\u001a\u00020\u00032\b\b\u0002\u0010 \u001a\u00020!H\u0007\u001a\u0016\u0010\"\u001a\u00020\u00012\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001d0\u0017H\u0007\u00a8\u0006#"}, d2 = {"DashboardHeader", "", "userName", "", "profilePictureUri", "onProfileClick", "Lkotlin/Function0;", "DashboardScreen", "navController", "Landroidx/navigation/NavController;", "viewModel", "Lcom/ml/tomatoscan/viewmodels/TomatoScanViewModel;", "userViewModel", "Lcom/ml/tomatoscan/viewmodels/UserViewModel;", "DiseaseCard", "disease", "Lcom/ml/tomatoscan/ui/screens/TomatoDisease;", "onClick", "DiseaseDetailDialog", "onDismiss", "DiseaseDetailSection", "title", "items", "", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "DiseaseInformationSection", "ScanHistoryChart", "scanHistory", "Lcom/ml/tomatoscan/models/ScanResult;", "StatCard", "value", "modifier", "Landroidx/compose/ui/Modifier;", "StatsSection", "app_debug"})
public final class DashboardScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class, androidx.compose.material.ExperimentalMaterialApi.class})
    @androidx.compose.runtime.Composable()
    public static final void DashboardScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.TomatoScanViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.UserViewModel userViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DashboardHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String userName, @org.jetbrains.annotations.Nullable()
    java.lang.String profilePictureUri, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onProfileClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StatsSection(@org.jetbrains.annotations.NotNull()
    java.util.List<com.ml.tomatoscan.models.ScanResult> scanHistory) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StatCard(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DiseaseInformationSection() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DiseaseCard(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.ui.screens.TomatoDisease disease, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ScanHistoryChart(@org.jetbrains.annotations.NotNull()
    java.util.List<com.ml.tomatoscan.models.ScanResult> scanHistory) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DiseaseDetailDialog(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.ui.screens.TomatoDisease disease, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DiseaseDetailSection(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> items, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.graphics.vector.ImageVector icon) {
    }
}