package com.ml.tomatoscan.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0014\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\u00a8\u0006\u0015"}, d2 = {"DarkGray", "Landroidx/compose/ui/graphics/Color;", "getDarkGray", "()J", "J", "DarkGreen", "getDarkGreen", "LightGreen", "getLightGreen", "LimeGreen", "getLimeGreen", "OffWhite", "getOffWhite", "ShadowColor", "getShadowColor", "TomatoRed", "getTomatoRed", "TomatoRedDark", "getTomatoRedDark", "White", "getWhite", "app_debug"})
public final class ColorKt {
    private static final long TomatoRed = 0L;
    private static final long DarkGreen = 0L;
    private static final long LightGreen = 0L;
    private static final long OffWhite = 0L;
    private static final long DarkGray = 0L;
    private static final long White = 0L;
    private static final long LimeGreen = 0L;
    private static final long TomatoRedDark = 0L;
    private static final long ShadowColor = 0L;
    
    public static final long getTomatoRed() {
        return 0L;
    }
    
    public static final long getDarkGreen() {
        return 0L;
    }
    
    public static final long getLightGreen() {
        return 0L;
    }
    
    public static final long getOffWhite() {
        return 0L;
    }
    
    public static final long getDarkGray() {
        return 0L;
    }
    
    public static final long getWhite() {
        return 0L;
    }
    
    public static final long getLimeGreen() {
        return 0L;
    }
    
    public static final long getTomatoRedDark() {
        return 0L;
    }
    
    public static final long getShadowColor() {
        return 0L;
    }
}