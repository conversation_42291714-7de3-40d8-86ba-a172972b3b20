package com.ml.tomatoscan.analysis;

/**
 * Analysis result data class with enhanced UI indicators
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u0000 \u00032\u00020\u0001:\u0003\u0003\u0004\u0005B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0002\u0006\u0007\u00a8\u0006\b"}, d2 = {"Lcom/ml/tomatoscan/analysis/AnalysisResult;", "", "()V", "Companion", "Error", "Success", "Lcom/ml/tomatoscan/analysis/AnalysisResult$Error;", "Lcom/ml/tomatoscan/analysis/AnalysisResult$Success;", "app_debug"})
public abstract class AnalysisResult {
    @org.jetbrains.annotations.NotNull()
    public static final com.ml.tomatoscan.analysis.AnalysisResult.Companion Companion = null;
    
    private AnalysisResult() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b\u00a8\u0006\t"}, d2 = {"Lcom/ml/tomatoscan/analysis/AnalysisResult$Companion;", "", "()V", "getAnalysisSourceDescription", "", "mode", "Lcom/ml/tomatoscan/analysis/AnalysisMode;", "isFallback", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * Get user-friendly description of analysis source for UI display
         */
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getAnalysisSourceDescription(@org.jetbrains.annotations.NotNull()
        com.ml.tomatoscan.analysis.AnalysisMode mode, boolean isFallback) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000f\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J1\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00072\b\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\r\u00a8\u0006\u001b"}, d2 = {"Lcom/ml/tomatoscan/analysis/AnalysisResult$Error;", "Lcom/ml/tomatoscan/analysis/AnalysisResult;", "message", "", "analysisMode", "Lcom/ml/tomatoscan/analysis/AnalysisMode;", "isOfflineFallback", "", "analysisSource", "(Ljava/lang/String;Lcom/ml/tomatoscan/analysis/AnalysisMode;ZLjava/lang/String;)V", "getAnalysisMode", "()Lcom/ml/tomatoscan/analysis/AnalysisMode;", "getAnalysisSource", "()Ljava/lang/String;", "()Z", "getMessage", "component1", "component2", "component3", "component4", "copy", "equals", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class Error extends com.ml.tomatoscan.analysis.AnalysisResult {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String message = null;
        @org.jetbrains.annotations.NotNull()
        private final com.ml.tomatoscan.analysis.AnalysisMode analysisMode = null;
        private final boolean isOfflineFallback = false;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String analysisSource = null;
        
        public Error(@org.jetbrains.annotations.NotNull()
        java.lang.String message, @org.jetbrains.annotations.NotNull()
        com.ml.tomatoscan.analysis.AnalysisMode analysisMode, boolean isOfflineFallback, @org.jetbrains.annotations.NotNull()
        java.lang.String analysisSource) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.ml.tomatoscan.analysis.AnalysisMode getAnalysisMode() {
            return null;
        }
        
        public final boolean isOfflineFallback() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getAnalysisSource() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.ml.tomatoscan.analysis.AnalysisMode component2() {
            return null;
        }
        
        public final boolean component3() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.ml.tomatoscan.analysis.AnalysisResult.Error copy(@org.jetbrains.annotations.NotNull()
        java.lang.String message, @org.jetbrains.annotations.NotNull()
        com.ml.tomatoscan.analysis.AnalysisMode analysisMode, boolean isOfflineFallback, @org.jetbrains.annotations.NotNull()
        java.lang.String analysisSource) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u001a\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BQ\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0011J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\bH\u00c6\u0003J\t\u0010#\u001a\u00020\nH\u00c6\u0003J\u000f\u0010$\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u00c6\u0003J\t\u0010%\u001a\u00020\u000fH\u00c6\u0003J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J_\u0010\'\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010(\u001a\u00020\u000f2\b\u0010)\u001a\u0004\u0018\u00010*H\u00d6\u0003J\t\u0010+\u001a\u00020,H\u00d6\u0001J\t\u0010-\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0017R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u001bR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0017\u00a8\u0006."}, d2 = {"Lcom/ml/tomatoscan/analysis/AnalysisResult$Success;", "Lcom/ml/tomatoscan/analysis/AnalysisResult;", "diseaseName", "", "confidence", "", "severity", "analysisMode", "Lcom/ml/tomatoscan/analysis/AnalysisMode;", "processingTime", "", "allClassifications", "", "Lcom/ml/tomatoscan/ml/DiseaseClassification;", "isOfflineFallback", "", "analysisSource", "(Ljava/lang/String;FLjava/lang/String;Lcom/ml/tomatoscan/analysis/AnalysisMode;JLjava/util/List;ZLjava/lang/String;)V", "getAllClassifications", "()Ljava/util/List;", "getAnalysisMode", "()Lcom/ml/tomatoscan/analysis/AnalysisMode;", "getAnalysisSource", "()Ljava/lang/String;", "getConfidence", "()F", "getDiseaseName", "()Z", "getProcessingTime", "()J", "getSeverity", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class Success extends com.ml.tomatoscan.analysis.AnalysisResult {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String diseaseName = null;
        private final float confidence = 0.0F;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String severity = null;
        @org.jetbrains.annotations.NotNull()
        private final com.ml.tomatoscan.analysis.AnalysisMode analysisMode = null;
        private final long processingTime = 0L;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.ml.tomatoscan.ml.DiseaseClassification> allClassifications = null;
        private final boolean isOfflineFallback = false;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String analysisSource = null;
        
        public Success(@org.jetbrains.annotations.NotNull()
        java.lang.String diseaseName, float confidence, @org.jetbrains.annotations.NotNull()
        java.lang.String severity, @org.jetbrains.annotations.NotNull()
        com.ml.tomatoscan.analysis.AnalysisMode analysisMode, long processingTime, @org.jetbrains.annotations.NotNull()
        java.util.List<com.ml.tomatoscan.ml.DiseaseClassification> allClassifications, boolean isOfflineFallback, @org.jetbrains.annotations.NotNull()
        java.lang.String analysisSource) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDiseaseName() {
            return null;
        }
        
        public final float getConfidence() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSeverity() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.ml.tomatoscan.analysis.AnalysisMode getAnalysisMode() {
            return null;
        }
        
        public final long getProcessingTime() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.ml.tomatoscan.ml.DiseaseClassification> getAllClassifications() {
            return null;
        }
        
        public final boolean isOfflineFallback() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getAnalysisSource() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final float component2() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.ml.tomatoscan.analysis.AnalysisMode component4() {
            return null;
        }
        
        public final long component5() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.ml.tomatoscan.ml.DiseaseClassification> component6() {
            return null;
        }
        
        public final boolean component7() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.ml.tomatoscan.analysis.AnalysisResult.Success copy(@org.jetbrains.annotations.NotNull()
        java.lang.String diseaseName, float confidence, @org.jetbrains.annotations.NotNull()
        java.lang.String severity, @org.jetbrains.annotations.NotNull()
        com.ml.tomatoscan.analysis.AnalysisMode analysisMode, long processingTime, @org.jetbrains.annotations.NotNull()
        java.util.List<com.ml.tomatoscan.ml.DiseaseClassification> allClassifications, boolean isOfflineFallback, @org.jetbrains.annotations.NotNull()
        java.lang.String analysisSource) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}