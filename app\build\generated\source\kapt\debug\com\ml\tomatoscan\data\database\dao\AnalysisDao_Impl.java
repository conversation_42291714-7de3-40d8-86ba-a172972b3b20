package com.ml.tomatoscan.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.ml.tomatoscan.data.database.converters.DateConverter;
import com.ml.tomatoscan.data.database.converters.StringListConverter;
import com.ml.tomatoscan.data.database.entities.AnalysisEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AnalysisDao_Impl implements AnalysisDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AnalysisEntity> __insertionAdapterOfAnalysisEntity;

  private final StringListConverter __stringListConverter = new StringListConverter();

  private final DateConverter __dateConverter = new DateConverter();

  private final EntityDeletionOrUpdateAdapter<AnalysisEntity> __deletionAdapterOfAnalysisEntity;

  private final EntityDeletionOrUpdateAdapter<AnalysisEntity> __updateAdapterOfAnalysisEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAnalysisById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllAnalyses;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldAnalyses;

  public AnalysisDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAnalysisEntity = new EntityInsertionAdapter<AnalysisEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `analysis_results` (`id`,`imageData`,`imagePath`,`diseaseDetected`,`severity`,`confidence`,`description`,`recommendations`,`treatmentOptions`,`preventionMeasures`,`timestamp`,`quality`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AnalysisEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getImageData() == null) {
          statement.bindNull(2);
        } else {
          statement.bindBlob(2, entity.getImageData());
        }
        if (entity.getImagePath() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getImagePath());
        }
        if (entity.getDiseaseDetected() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDiseaseDetected());
        }
        if (entity.getSeverity() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSeverity());
        }
        statement.bindDouble(6, entity.getConfidence());
        if (entity.getDescription() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getDescription());
        }
        final String _tmp = __stringListConverter.fromStringList(entity.getRecommendations());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final String _tmp_1 = __stringListConverter.fromStringList(entity.getTreatmentOptions());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        final String _tmp_2 = __stringListConverter.fromStringList(entity.getPreventionMeasures());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        final Long _tmp_3 = __dateConverter.dateToTimestamp(entity.getTimestamp());
        if (_tmp_3 == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, _tmp_3);
        }
        if (entity.getQuality() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getQuality());
        }
      }
    };
    this.__deletionAdapterOfAnalysisEntity = new EntityDeletionOrUpdateAdapter<AnalysisEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `analysis_results` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AnalysisEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfAnalysisEntity = new EntityDeletionOrUpdateAdapter<AnalysisEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `analysis_results` SET `id` = ?,`imageData` = ?,`imagePath` = ?,`diseaseDetected` = ?,`severity` = ?,`confidence` = ?,`description` = ?,`recommendations` = ?,`treatmentOptions` = ?,`preventionMeasures` = ?,`timestamp` = ?,`quality` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AnalysisEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getImageData() == null) {
          statement.bindNull(2);
        } else {
          statement.bindBlob(2, entity.getImageData());
        }
        if (entity.getImagePath() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getImagePath());
        }
        if (entity.getDiseaseDetected() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDiseaseDetected());
        }
        if (entity.getSeverity() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSeverity());
        }
        statement.bindDouble(6, entity.getConfidence());
        if (entity.getDescription() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getDescription());
        }
        final String _tmp = __stringListConverter.fromStringList(entity.getRecommendations());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final String _tmp_1 = __stringListConverter.fromStringList(entity.getTreatmentOptions());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        final String _tmp_2 = __stringListConverter.fromStringList(entity.getPreventionMeasures());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        final Long _tmp_3 = __dateConverter.dateToTimestamp(entity.getTimestamp());
        if (_tmp_3 == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, _tmp_3);
        }
        if (entity.getQuality() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getQuality());
        }
        statement.bindLong(13, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAnalysisById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM analysis_results WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllAnalyses = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM analysis_results";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldAnalyses = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM analysis_results WHERE timestamp < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertAnalysis(final AnalysisEntity analysis,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfAnalysisEntity.insertAndReturnId(analysis);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAnalyses(final List<AnalysisEntity> analyses,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfAnalysisEntity.insert(analyses);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAnalysis(final AnalysisEntity analysis,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfAnalysisEntity.handle(analysis);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAnalysis(final AnalysisEntity analysis,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfAnalysisEntity.handle(analysis);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAnalysisById(final long id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAnalysisById.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAnalysisById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllAnalyses(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllAnalyses.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllAnalyses.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldAnalyses(final long cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldAnalyses.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, cutoffDate);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldAnalyses.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<AnalysisEntity>> getAllAnalyses() {
    final String _sql = "SELECT * FROM analysis_results ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"analysis_results"}, new Callable<List<AnalysisEntity>>() {
      @Override
      @NonNull
      public List<AnalysisEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfImageData = CursorUtil.getColumnIndexOrThrow(_cursor, "imageData");
          final int _cursorIndexOfImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "imagePath");
          final int _cursorIndexOfDiseaseDetected = CursorUtil.getColumnIndexOrThrow(_cursor, "diseaseDetected");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfRecommendations = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendations");
          final int _cursorIndexOfTreatmentOptions = CursorUtil.getColumnIndexOrThrow(_cursor, "treatmentOptions");
          final int _cursorIndexOfPreventionMeasures = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionMeasures");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final List<AnalysisEntity> _result = new ArrayList<AnalysisEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AnalysisEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final byte[] _tmpImageData;
            if (_cursor.isNull(_cursorIndexOfImageData)) {
              _tmpImageData = null;
            } else {
              _tmpImageData = _cursor.getBlob(_cursorIndexOfImageData);
            }
            final String _tmpImagePath;
            if (_cursor.isNull(_cursorIndexOfImagePath)) {
              _tmpImagePath = null;
            } else {
              _tmpImagePath = _cursor.getString(_cursorIndexOfImagePath);
            }
            final String _tmpDiseaseDetected;
            if (_cursor.isNull(_cursorIndexOfDiseaseDetected)) {
              _tmpDiseaseDetected = null;
            } else {
              _tmpDiseaseDetected = _cursor.getString(_cursorIndexOfDiseaseDetected);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpRecommendations;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfRecommendations)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfRecommendations);
            }
            _tmpRecommendations = __stringListConverter.toStringList(_tmp);
            final List<String> _tmpTreatmentOptions;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTreatmentOptions)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTreatmentOptions);
            }
            _tmpTreatmentOptions = __stringListConverter.toStringList(_tmp_1);
            final List<String> _tmpPreventionMeasures;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfPreventionMeasures)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfPreventionMeasures);
            }
            _tmpPreventionMeasures = __stringListConverter.toStringList(_tmp_2);
            final Date _tmpTimestamp;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __dateConverter.fromTimestamp(_tmp_3);
            final String _tmpQuality;
            if (_cursor.isNull(_cursorIndexOfQuality)) {
              _tmpQuality = null;
            } else {
              _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            }
            _item = new AnalysisEntity(_tmpId,_tmpImageData,_tmpImagePath,_tmpDiseaseDetected,_tmpSeverity,_tmpConfidence,_tmpDescription,_tmpRecommendations,_tmpTreatmentOptions,_tmpPreventionMeasures,_tmpTimestamp,_tmpQuality);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AnalysisEntity>> getRecentAnalyses(final int limit) {
    final String _sql = "SELECT * FROM analysis_results ORDER BY timestamp DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"analysis_results"}, new Callable<List<AnalysisEntity>>() {
      @Override
      @NonNull
      public List<AnalysisEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfImageData = CursorUtil.getColumnIndexOrThrow(_cursor, "imageData");
          final int _cursorIndexOfImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "imagePath");
          final int _cursorIndexOfDiseaseDetected = CursorUtil.getColumnIndexOrThrow(_cursor, "diseaseDetected");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfRecommendations = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendations");
          final int _cursorIndexOfTreatmentOptions = CursorUtil.getColumnIndexOrThrow(_cursor, "treatmentOptions");
          final int _cursorIndexOfPreventionMeasures = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionMeasures");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final List<AnalysisEntity> _result = new ArrayList<AnalysisEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AnalysisEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final byte[] _tmpImageData;
            if (_cursor.isNull(_cursorIndexOfImageData)) {
              _tmpImageData = null;
            } else {
              _tmpImageData = _cursor.getBlob(_cursorIndexOfImageData);
            }
            final String _tmpImagePath;
            if (_cursor.isNull(_cursorIndexOfImagePath)) {
              _tmpImagePath = null;
            } else {
              _tmpImagePath = _cursor.getString(_cursorIndexOfImagePath);
            }
            final String _tmpDiseaseDetected;
            if (_cursor.isNull(_cursorIndexOfDiseaseDetected)) {
              _tmpDiseaseDetected = null;
            } else {
              _tmpDiseaseDetected = _cursor.getString(_cursorIndexOfDiseaseDetected);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpRecommendations;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfRecommendations)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfRecommendations);
            }
            _tmpRecommendations = __stringListConverter.toStringList(_tmp);
            final List<String> _tmpTreatmentOptions;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTreatmentOptions)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTreatmentOptions);
            }
            _tmpTreatmentOptions = __stringListConverter.toStringList(_tmp_1);
            final List<String> _tmpPreventionMeasures;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfPreventionMeasures)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfPreventionMeasures);
            }
            _tmpPreventionMeasures = __stringListConverter.toStringList(_tmp_2);
            final Date _tmpTimestamp;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __dateConverter.fromTimestamp(_tmp_3);
            final String _tmpQuality;
            if (_cursor.isNull(_cursorIndexOfQuality)) {
              _tmpQuality = null;
            } else {
              _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            }
            _item = new AnalysisEntity(_tmpId,_tmpImageData,_tmpImagePath,_tmpDiseaseDetected,_tmpSeverity,_tmpConfidence,_tmpDescription,_tmpRecommendations,_tmpTreatmentOptions,_tmpPreventionMeasures,_tmpTimestamp,_tmpQuality);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAnalysisById(final long id,
      final Continuation<? super AnalysisEntity> $completion) {
    final String _sql = "SELECT * FROM analysis_results WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<AnalysisEntity>() {
      @Override
      @Nullable
      public AnalysisEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfImageData = CursorUtil.getColumnIndexOrThrow(_cursor, "imageData");
          final int _cursorIndexOfImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "imagePath");
          final int _cursorIndexOfDiseaseDetected = CursorUtil.getColumnIndexOrThrow(_cursor, "diseaseDetected");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfRecommendations = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendations");
          final int _cursorIndexOfTreatmentOptions = CursorUtil.getColumnIndexOrThrow(_cursor, "treatmentOptions");
          final int _cursorIndexOfPreventionMeasures = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionMeasures");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final AnalysisEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final byte[] _tmpImageData;
            if (_cursor.isNull(_cursorIndexOfImageData)) {
              _tmpImageData = null;
            } else {
              _tmpImageData = _cursor.getBlob(_cursorIndexOfImageData);
            }
            final String _tmpImagePath;
            if (_cursor.isNull(_cursorIndexOfImagePath)) {
              _tmpImagePath = null;
            } else {
              _tmpImagePath = _cursor.getString(_cursorIndexOfImagePath);
            }
            final String _tmpDiseaseDetected;
            if (_cursor.isNull(_cursorIndexOfDiseaseDetected)) {
              _tmpDiseaseDetected = null;
            } else {
              _tmpDiseaseDetected = _cursor.getString(_cursorIndexOfDiseaseDetected);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpRecommendations;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfRecommendations)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfRecommendations);
            }
            _tmpRecommendations = __stringListConverter.toStringList(_tmp);
            final List<String> _tmpTreatmentOptions;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTreatmentOptions)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTreatmentOptions);
            }
            _tmpTreatmentOptions = __stringListConverter.toStringList(_tmp_1);
            final List<String> _tmpPreventionMeasures;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfPreventionMeasures)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfPreventionMeasures);
            }
            _tmpPreventionMeasures = __stringListConverter.toStringList(_tmp_2);
            final Date _tmpTimestamp;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __dateConverter.fromTimestamp(_tmp_3);
            final String _tmpQuality;
            if (_cursor.isNull(_cursorIndexOfQuality)) {
              _tmpQuality = null;
            } else {
              _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            }
            _result = new AnalysisEntity(_tmpId,_tmpImageData,_tmpImagePath,_tmpDiseaseDetected,_tmpSeverity,_tmpConfidence,_tmpDescription,_tmpRecommendations,_tmpTreatmentOptions,_tmpPreventionMeasures,_tmpTimestamp,_tmpQuality);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object findAnalysisByTimestamp(final Date timestamp,
      final Continuation<? super AnalysisEntity> $completion) {
    final String _sql = "SELECT * FROM analysis_results WHERE timestamp = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __dateConverter.dateToTimestamp(timestamp);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<AnalysisEntity>() {
      @Override
      @Nullable
      public AnalysisEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfImageData = CursorUtil.getColumnIndexOrThrow(_cursor, "imageData");
          final int _cursorIndexOfImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "imagePath");
          final int _cursorIndexOfDiseaseDetected = CursorUtil.getColumnIndexOrThrow(_cursor, "diseaseDetected");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfRecommendations = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendations");
          final int _cursorIndexOfTreatmentOptions = CursorUtil.getColumnIndexOrThrow(_cursor, "treatmentOptions");
          final int _cursorIndexOfPreventionMeasures = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionMeasures");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final AnalysisEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final byte[] _tmpImageData;
            if (_cursor.isNull(_cursorIndexOfImageData)) {
              _tmpImageData = null;
            } else {
              _tmpImageData = _cursor.getBlob(_cursorIndexOfImageData);
            }
            final String _tmpImagePath;
            if (_cursor.isNull(_cursorIndexOfImagePath)) {
              _tmpImagePath = null;
            } else {
              _tmpImagePath = _cursor.getString(_cursorIndexOfImagePath);
            }
            final String _tmpDiseaseDetected;
            if (_cursor.isNull(_cursorIndexOfDiseaseDetected)) {
              _tmpDiseaseDetected = null;
            } else {
              _tmpDiseaseDetected = _cursor.getString(_cursorIndexOfDiseaseDetected);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpRecommendations;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfRecommendations)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfRecommendations);
            }
            _tmpRecommendations = __stringListConverter.toStringList(_tmp_1);
            final List<String> _tmpTreatmentOptions;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTreatmentOptions)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTreatmentOptions);
            }
            _tmpTreatmentOptions = __stringListConverter.toStringList(_tmp_2);
            final List<String> _tmpPreventionMeasures;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfPreventionMeasures)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfPreventionMeasures);
            }
            _tmpPreventionMeasures = __stringListConverter.toStringList(_tmp_3);
            final Date _tmpTimestamp;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __dateConverter.fromTimestamp(_tmp_4);
            final String _tmpQuality;
            if (_cursor.isNull(_cursorIndexOfQuality)) {
              _tmpQuality = null;
            } else {
              _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            }
            _result = new AnalysisEntity(_tmpId,_tmpImageData,_tmpImagePath,_tmpDiseaseDetected,_tmpSeverity,_tmpConfidence,_tmpDescription,_tmpRecommendations,_tmpTreatmentOptions,_tmpPreventionMeasures,_tmpTimestamp,_tmpQuality);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAnalysesByDisease(final String disease,
      final Continuation<? super List<AnalysisEntity>> $completion) {
    final String _sql = "SELECT * FROM analysis_results WHERE diseaseDetected LIKE ? ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (disease == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, disease);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AnalysisEntity>>() {
      @Override
      @NonNull
      public List<AnalysisEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfImageData = CursorUtil.getColumnIndexOrThrow(_cursor, "imageData");
          final int _cursorIndexOfImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "imagePath");
          final int _cursorIndexOfDiseaseDetected = CursorUtil.getColumnIndexOrThrow(_cursor, "diseaseDetected");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfRecommendations = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendations");
          final int _cursorIndexOfTreatmentOptions = CursorUtil.getColumnIndexOrThrow(_cursor, "treatmentOptions");
          final int _cursorIndexOfPreventionMeasures = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionMeasures");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final List<AnalysisEntity> _result = new ArrayList<AnalysisEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AnalysisEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final byte[] _tmpImageData;
            if (_cursor.isNull(_cursorIndexOfImageData)) {
              _tmpImageData = null;
            } else {
              _tmpImageData = _cursor.getBlob(_cursorIndexOfImageData);
            }
            final String _tmpImagePath;
            if (_cursor.isNull(_cursorIndexOfImagePath)) {
              _tmpImagePath = null;
            } else {
              _tmpImagePath = _cursor.getString(_cursorIndexOfImagePath);
            }
            final String _tmpDiseaseDetected;
            if (_cursor.isNull(_cursorIndexOfDiseaseDetected)) {
              _tmpDiseaseDetected = null;
            } else {
              _tmpDiseaseDetected = _cursor.getString(_cursorIndexOfDiseaseDetected);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpRecommendations;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfRecommendations)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfRecommendations);
            }
            _tmpRecommendations = __stringListConverter.toStringList(_tmp);
            final List<String> _tmpTreatmentOptions;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTreatmentOptions)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTreatmentOptions);
            }
            _tmpTreatmentOptions = __stringListConverter.toStringList(_tmp_1);
            final List<String> _tmpPreventionMeasures;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfPreventionMeasures)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfPreventionMeasures);
            }
            _tmpPreventionMeasures = __stringListConverter.toStringList(_tmp_2);
            final Date _tmpTimestamp;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __dateConverter.fromTimestamp(_tmp_3);
            final String _tmpQuality;
            if (_cursor.isNull(_cursorIndexOfQuality)) {
              _tmpQuality = null;
            } else {
              _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            }
            _item = new AnalysisEntity(_tmpId,_tmpImageData,_tmpImagePath,_tmpDiseaseDetected,_tmpSeverity,_tmpConfidence,_tmpDescription,_tmpRecommendations,_tmpTreatmentOptions,_tmpPreventionMeasures,_tmpTimestamp,_tmpQuality);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAnalysesBySeverity(final String severity,
      final Continuation<? super List<AnalysisEntity>> $completion) {
    final String _sql = "SELECT * FROM analysis_results WHERE severity = ? ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (severity == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, severity);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AnalysisEntity>>() {
      @Override
      @NonNull
      public List<AnalysisEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfImageData = CursorUtil.getColumnIndexOrThrow(_cursor, "imageData");
          final int _cursorIndexOfImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "imagePath");
          final int _cursorIndexOfDiseaseDetected = CursorUtil.getColumnIndexOrThrow(_cursor, "diseaseDetected");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfRecommendations = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendations");
          final int _cursorIndexOfTreatmentOptions = CursorUtil.getColumnIndexOrThrow(_cursor, "treatmentOptions");
          final int _cursorIndexOfPreventionMeasures = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionMeasures");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final List<AnalysisEntity> _result = new ArrayList<AnalysisEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AnalysisEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final byte[] _tmpImageData;
            if (_cursor.isNull(_cursorIndexOfImageData)) {
              _tmpImageData = null;
            } else {
              _tmpImageData = _cursor.getBlob(_cursorIndexOfImageData);
            }
            final String _tmpImagePath;
            if (_cursor.isNull(_cursorIndexOfImagePath)) {
              _tmpImagePath = null;
            } else {
              _tmpImagePath = _cursor.getString(_cursorIndexOfImagePath);
            }
            final String _tmpDiseaseDetected;
            if (_cursor.isNull(_cursorIndexOfDiseaseDetected)) {
              _tmpDiseaseDetected = null;
            } else {
              _tmpDiseaseDetected = _cursor.getString(_cursorIndexOfDiseaseDetected);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final float _tmpConfidence;
            _tmpConfidence = _cursor.getFloat(_cursorIndexOfConfidence);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpRecommendations;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfRecommendations)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfRecommendations);
            }
            _tmpRecommendations = __stringListConverter.toStringList(_tmp);
            final List<String> _tmpTreatmentOptions;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTreatmentOptions)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTreatmentOptions);
            }
            _tmpTreatmentOptions = __stringListConverter.toStringList(_tmp_1);
            final List<String> _tmpPreventionMeasures;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfPreventionMeasures)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfPreventionMeasures);
            }
            _tmpPreventionMeasures = __stringListConverter.toStringList(_tmp_2);
            final Date _tmpTimestamp;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __dateConverter.fromTimestamp(_tmp_3);
            final String _tmpQuality;
            if (_cursor.isNull(_cursorIndexOfQuality)) {
              _tmpQuality = null;
            } else {
              _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            }
            _item = new AnalysisEntity(_tmpId,_tmpImageData,_tmpImagePath,_tmpDiseaseDetected,_tmpSeverity,_tmpConfidence,_tmpDescription,_tmpRecommendations,_tmpTreatmentOptions,_tmpPreventionMeasures,_tmpTimestamp,_tmpQuality);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAnalysisCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM analysis_results";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getDiseaseStatistics(
      final Continuation<? super List<DiseaseStatistic>> $completion) {
    final String _sql = "SELECT diseaseDetected, COUNT(*) as count FROM analysis_results GROUP BY diseaseDetected ORDER BY count DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<DiseaseStatistic>>() {
      @Override
      @NonNull
      public List<DiseaseStatistic> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfDiseaseDetected = 0;
          final int _cursorIndexOfCount = 1;
          final List<DiseaseStatistic> _result = new ArrayList<DiseaseStatistic>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DiseaseStatistic _item;
            final String _tmpDiseaseDetected;
            if (_cursor.isNull(_cursorIndexOfDiseaseDetected)) {
              _tmpDiseaseDetected = null;
            } else {
              _tmpDiseaseDetected = _cursor.getString(_cursorIndexOfDiseaseDetected);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new DiseaseStatistic(_tmpDiseaseDetected,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getSeverityStatistics(
      final Continuation<? super List<SeverityStatistic>> $completion) {
    final String _sql = "SELECT severity, COUNT(*) as count FROM analysis_results GROUP BY severity ORDER BY count DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SeverityStatistic>>() {
      @Override
      @NonNull
      public List<SeverityStatistic> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSeverity = 0;
          final int _cursorIndexOfCount = 1;
          final List<SeverityStatistic> _result = new ArrayList<SeverityStatistic>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SeverityStatistic _item;
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new SeverityStatistic(_tmpSeverity,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageConfidence(final Continuation<? super Float> $completion) {
    final String _sql = "SELECT AVG(confidence) as averageConfidence FROM analysis_results";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Float>() {
      @Override
      @Nullable
      public Float call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Float _result;
          if (_cursor.moveToFirst()) {
            final Float _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getFloat(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
