{"logs": [{"outputFile": "com.ml.tomatoscan.app-mergeDebugResources-80:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\398c2a74b03599659b5ead5e2fab5e05\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,287,373,472,575,665,745,841,931,1018,1107,1198,1270,1360,1438,1516,1591,1670,1740", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "282,368,467,570,660,740,836,926,1013,1102,1193,1265,1355,1433,1511,1586,1665,1735,1856"}, "to": {"startLines": "39,40,60,61,62,66,67,126,127,128,129,131,132,133,134,135,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3850,3946,6358,6457,6560,6968,7048,13502,13592,13679,13768,13941,14013,14103,14181,14259,14435,14514,14584", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "3941,4027,6452,6555,6645,7043,7139,13587,13674,13763,13854,14008,14098,14176,14254,14329,14509,14579,14700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\827848161c01d8e105936428022348b0\\transformed\\material3-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,411,525,625,724,840,976,1094,1242,1328,1430,1524,1622,1744,1864,1971,2106,2243,2378,2550,2679,2796,2914,3035,3130,3227,3345,3484,3587,3689,3800,3938,4078,4189,4292,4369,4464,4562,4672,4758,4845,4958,5038,5123,5224,5327,5421,5523,5609,5715,5811,5919,6036,6116,6222", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "168,285,406,520,620,719,835,971,1089,1237,1323,1425,1519,1617,1739,1859,1966,2101,2238,2373,2545,2674,2791,2909,3030,3125,3222,3340,3479,3582,3684,3795,3933,4073,4184,4287,4364,4459,4557,4667,4753,4840,4953,5033,5118,5219,5322,5416,5518,5604,5710,5806,5914,6031,6111,6217,6314"}, "to": {"startLines": "68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7144,7262,7379,7500,7614,7714,7813,7929,8065,8183,8331,8417,8519,8613,8711,8833,8953,9060,9195,9332,9467,9639,9768,9885,10003,10124,10219,10316,10434,10573,10676,10778,10889,11027,11167,11278,11381,11458,11553,11651,11761,11847,11934,12047,12127,12212,12313,12416,12510,12612,12698,12804,12900,13008,13125,13205,13311", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "7257,7374,7495,7609,7709,7808,7924,8060,8178,8326,8412,8514,8608,8706,8828,8948,9055,9190,9327,9462,9634,9763,9880,9998,10119,10214,10311,10429,10568,10671,10773,10884,11022,11162,11273,11376,11453,11548,11646,11756,11842,11929,12042,12122,12207,12308,12411,12505,12607,12693,12799,12895,13003,13120,13200,13306,13403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a6bb74210ae1d7265ae291f685894631\\transformed\\material-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "13408", "endColumns": "93", "endOffsets": "13497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e33c8e88ddf3587b7d540033450542b0\\transformed\\play-services-base-18.1.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4032,4137,4286,4414,4524,4678,4812,4934,5185,5358,5466,5621,5749,5910,6049,6115,6176", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "4132,4281,4409,4519,4673,4807,4929,5037,5353,5461,5616,5744,5905,6044,6110,6171,6247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5468497be68ad4ba67415839697dae57\\transformed\\credentials-1.2.0-rc01\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,122", "endOffsets": "160,283"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2808,2918", "endColumns": "109,122", "endOffsets": "2913,3036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aef2b099c308afe7b74c31a0cefc610e\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,228", "endColumns": "84,87,90", "endOffsets": "135,223,314"}, "to": {"startLines": "31,140,141", "startColumns": "4,4,4", "startOffsets": "3041,14705,14793", "endColumns": "84,87,90", "endOffsets": "3121,14788,14879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf21ceb9549d3cbcfc1e34a7f2650da\\transformed\\core-1.16.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "32,33,34,35,36,37,38,136", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3126,3222,3324,3423,3522,3628,3732,14334", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3217,3319,3418,3517,3623,3727,3845,14430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab6cbaf7bf230b6b6d5af555fdbb71dd\\transformed\\browser-1.4.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,104", "endOffsets": "156,259,369,474"}, "to": {"startLines": "59,63,64,65", "startColumns": "4,4,4,4", "startOffsets": "6252,6650,6753,6863", "endColumns": "105,102,109,104", "endOffsets": "6353,6748,6858,6963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d934593cab4de80219eb571e0fe7548b\\transformed\\play-services-basement-18.3.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5042", "endColumns": "142", "endOffsets": "5180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3d5cb58bacb9a0b03b60afe3e3ab7d36\\transformed\\appcompat-1.6.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,13859", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,13936"}}]}]}