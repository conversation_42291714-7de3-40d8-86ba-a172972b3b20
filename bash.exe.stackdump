Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FEBA
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210285FF9, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E80  0002100690B4 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9160  00021006A49D (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFAE900000 ntdll.dll
7FFFADA40000 KERNEL32.DLL
7FFFAC000000 KERNELBASE.dll
000210040000 msys-2.0.dll
7FFFAC6E0000 USER32.dll
7FFFABC80000 win32u.dll
7FFFAE290000 GDI32.dll
7FFFABEC0000 gdi32full.dll
7FFFABBD0000 msvcp_win.dll
7FFFABD70000 ucrtbase.dll
7FFFAE2C0000 advapi32.dll
7FFFADCF0000 msvcrt.dll
7FFFAD110000 sechost.dll
7FFFAE170000 RPCRT4.dll
7FFFAB000000 CRYPTBASE.DLL
7FFFAC570000 bcryptPrimitives.dll
7FFFAD4C0000 IMM32.DLL
7FFF913D0000 windhawk.dll
7FFFA4AA0000 WINHTTP.dll
