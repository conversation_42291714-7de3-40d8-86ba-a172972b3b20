[{"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-hdpi_app_logo_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-hdpi/app_logo_round.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-mdpi_app_logo_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-mdpi/app_logo_round.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_disease_mosaic_virus.jpeg.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/disease_mosaic_virus.jpeg"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xhdpi_app_logo_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xhdpi/app_logo_round.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_tomato_leaf.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/tomato_leaf.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_ic_launcher_foreground.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxxhdpi_app_logo.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxxhdpi/app_logo.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/xml_data_extraction_rules.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/xml/data_extraction_rules.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/xml_backup_rules.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/xml/backup_rules.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_disease_early_blight.jpeg.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/disease_early_blight.jpeg"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-mdpi_app_logo.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-mdpi/app_logo.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_ic_launcher_background.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/ic_launcher_background.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxhdpi_app_logo.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxhdpi/app_logo.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_app_logo.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/app_logo.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_disease_fusarium_wilt.jpeg.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/disease_fusarium_wilt.jpeg"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxhdpi_app_logo_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxhdpi/app_logo_round.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_disease_late_blight.jpeg.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/disease_late_blight.jpeg"}, {"merged": "com.ml.tomatoscan.app-debug-82:/xml_file_paths.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/xml/file_paths.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xhdpi_app_logo.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xhdpi/app_logo.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxxhdpi_app_logo_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxxhdpi/app_logo_round.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-anydpi-v26_app_logo.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-anydpi-v26/app_logo.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_disease_septoria_leaf_spot.jpeg.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/disease_septoria_leaf_spot.jpeg"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_disease_bacterial_spot.jpg.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/disease_bacterial_spot.jpg"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-hdpi_app_logo.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-hdpi/app_logo.webp"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-anydpi-v26_app_logo_round.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-anydpi-v26/app_logo_round.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/drawable_scan_icon.xml.flat", "source": "com.ml.tomatoscan.app-main-84:/drawable/scan_icon.xml"}, {"merged": "com.ml.tomatoscan.app-debug-82:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.ml.tomatoscan.app-main-84:/mipmap-xxhdpi/ic_launcher_round.webp"}]