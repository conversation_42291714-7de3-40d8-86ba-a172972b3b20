{"logs": [{"outputFile": "com.ml.tomatoscan.app-mergeDebugResources-80:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\398c2a74b03599659b5ead5e2fab5e05\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180,277,361,457,557,646,730,823,914,999,1081,1167,1246,1333,1408,1486,1563,1640,1709", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,86,74,77,76,76,68,117", "endOffsets": "272,356,452,552,641,725,818,909,994,1076,1162,1241,1328,1403,1481,1558,1635,1704,1822"}, "to": {"startLines": "39,40,60,61,62,66,67,126,127,128,129,131,132,133,134,135,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3906,4003,6437,6533,6633,7039,7123,13721,13812,13897,13979,14148,14227,14314,14389,14467,14645,14722,14791", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,86,74,77,76,76,68,117", "endOffsets": "3998,4082,6528,6628,6717,7118,7211,13807,13892,13974,14060,14222,14309,14384,14462,14539,14717,14786,14904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5468497be68ad4ba67415839697dae57\\transformed\\credentials-1.2.0-rc01\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,113", "endOffsets": "164,278"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2859,2973", "endColumns": "113,113", "endOffsets": "2968,3082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aef2b099c308afe7b74c31a0cefc610e\\transformed\\foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,217", "endColumns": "73,87,94", "endOffsets": "124,212,307"}, "to": {"startLines": "31,140,141", "startColumns": "4,4,4", "startOffsets": "3087,14909,14997", "endColumns": "73,87,94", "endOffsets": "3156,14992,15087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab6cbaf7bf230b6b6d5af555fdbb71dd\\transformed\\browser-1.4.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "59,63,64,65", "startColumns": "4,4,4,4", "startOffsets": "6323,6722,6822,6938", "endColumns": "113,99,115,100", "endOffsets": "6432,6817,6933,7034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e33c8e88ddf3587b7d540033450542b0\\transformed\\play-services-base-18.1.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4087,4198,4368,4501,4616,4759,4888,4996,5241,5391,5504,5669,5804,5949,6106,6175,6238", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "4193,4363,4496,4611,4754,4883,4991,5090,5386,5499,5664,5799,5944,6101,6170,6233,6318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf21ceb9549d3cbcfc1e34a7f2650da\\transformed\\core-1.16.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "32,33,34,35,36,37,38,136", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3161,3259,3362,3463,3569,3670,3778,14544", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3254,3357,3458,3564,3665,3773,3901,14640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\827848161c01d8e105936428022348b0\\transformed\\material3-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,425,548,647,745,860,1017,1147,1299,1385,1491,1587,1689,1805,1938,2049,2188,2323,2456,2634,2758,2876,2997,3124,3221,3318,3440,3578,3684,3793,3899,4038,4183,4293,4402,4478,4578,4678,4794,4881,4970,5081,5161,5245,5345,5453,5553,5654,5741,5854,5956,6061,6182,6262,6372", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,115,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "174,298,420,543,642,740,855,1012,1142,1294,1380,1486,1582,1684,1800,1933,2044,2183,2318,2451,2629,2753,2871,2992,3119,3216,3313,3435,3573,3679,3788,3894,4033,4178,4288,4397,4473,4573,4673,4789,4876,4965,5076,5156,5240,5340,5448,5548,5649,5736,5849,5951,6056,6177,6257,6367,6464"}, "to": {"startLines": "68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7216,7340,7464,7586,7709,7808,7906,8021,8178,8308,8460,8546,8652,8748,8850,8966,9099,9210,9349,9484,9617,9795,9919,10037,10158,10285,10382,10479,10601,10739,10845,10954,11060,11199,11344,11454,11563,11639,11739,11839,11955,12042,12131,12242,12322,12406,12506,12614,12714,12815,12902,13015,13117,13222,13343,13423,13533", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,115,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "7335,7459,7581,7704,7803,7901,8016,8173,8303,8455,8541,8647,8743,8845,8961,9094,9205,9344,9479,9612,9790,9914,10032,10153,10280,10377,10474,10596,10734,10840,10949,11055,11194,11339,11449,11558,11634,11734,11834,11950,12037,12126,12237,12317,12401,12501,12609,12709,12810,12897,13010,13112,13217,13338,13418,13528,13625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3d5cb58bacb9a0b03b60afe3e3ab7d36\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,14065", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,14143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a6bb74210ae1d7265ae291f685894631\\transformed\\material-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "13630", "endColumns": "90", "endOffsets": "13716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d934593cab4de80219eb571e0fe7548b\\transformed\\play-services-basement-18.3.0\\res\\values-kn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5095", "endColumns": "145", "endOffsets": "5236"}}]}]}