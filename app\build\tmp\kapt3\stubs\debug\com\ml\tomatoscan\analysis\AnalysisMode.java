package com.ml.tomatoscan.analysis;

/**
 * Analysis modes
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/ml/tomatoscan/analysis/AnalysisMode;", "", "(Ljava/lang/String;I)V", "AUTO", "OFFLINE_ONLY", "ONLINE_ONLY", "app_debug"})
public enum AnalysisMode {
    /*public static final*/ AUTO /* = new AUTO() */,
    /*public static final*/ OFFLINE_ONLY /* = new OFFLINE_ONLY() */,
    /*public static final*/ ONLINE_ONLY /* = new ONLINE_ONLY() */;
    
    AnalysisMode() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.ml.tomatoscan.analysis.AnalysisMode> getEntries() {
        return null;
    }
}