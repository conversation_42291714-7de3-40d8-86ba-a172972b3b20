{"logs": [{"outputFile": "com.ml.tomatoscan.app-mergeDebugResources-80:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d934593cab4de80219eb571e0fe7548b\\transformed\\play-services-basement-18.3.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4637", "endColumns": "102", "endOffsets": "4735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf21ceb9549d3cbcfc1e34a7f2650da\\transformed\\core-1.16.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "32,33,34,35,36,37,38,136", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2959,3051,3150,3244,3338,3431,3524,12731", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3046,3145,3239,3333,3426,3519,3615,12827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\398c2a74b03599659b5ead5e2fab5e05\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,254,330,417,508,586,660,737,815,890,963,1038,1106,1187,1260,1332,1403,1477,1545", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,80,72,71,70,73,67,115", "endOffsets": "249,325,412,503,581,655,732,810,885,958,1033,1101,1182,1255,1327,1398,1472,1540,1656"}, "to": {"startLines": "39,40,60,61,62,66,67,126,127,128,129,131,132,133,134,135,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3620,3697,5676,5763,5854,6218,6292,11986,12064,12139,12212,12366,12434,12515,12588,12660,12832,12906,12974", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,80,72,71,70,73,67,115", "endOffsets": "3692,3768,5758,5849,5927,6287,6364,12059,12134,12207,12282,12429,12510,12583,12655,12726,12901,12969,13085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3d5cb58bacb9a0b03b60afe3e3ab7d36\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,12287", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,12361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab6cbaf7bf230b6b6d5af555fdbb71dd\\transformed\\browser-1.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "59,63,64,65", "startColumns": "4,4,4,4", "startOffsets": "5592,5932,6024,6125", "endColumns": "83,91,100,92", "endOffsets": "5671,6019,6120,6213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aef2b099c308afe7b74c31a0cefc610e\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,209", "endColumns": "70,82,78", "endOffsets": "121,204,283"}, "to": {"startLines": "31,140,141", "startColumns": "4,4,4", "startOffsets": "2888,13090,13173", "endColumns": "70,82,78", "endOffsets": "2954,13168,13247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5468497be68ad4ba67415839697dae57\\transformed\\credentials-1.2.0-rc01\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,110", "endOffsets": "156,267"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2671,2777", "endColumns": "105,110", "endOffsets": "2772,2883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e33c8e88ddf3587b7d540033450542b0\\transformed\\play-services-base-18.1.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3773,3874,4002,4117,4219,4326,4442,4542,4740,4850,4951,5080,5195,5297,5405,5461,5518", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "3869,3997,4112,4214,4321,4437,4537,4632,4845,4946,5075,5190,5292,5400,5456,5513,5587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\827848161c01d8e105936428022348b0\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4048,4131,4213,4302,4382,4464,4561,4655,4748,4841,4925,5022,5118,5213,5321,5401,5495", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4043,4126,4208,4297,4377,4459,4556,4650,4743,4836,4920,5017,5113,5208,5316,5396,5490,5582"}, "to": {"startLines": "68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6369,6473,6576,6680,6782,6874,6962,7066,7171,7276,7392,7474,7570,7654,7742,7847,7960,8061,8170,8277,8385,8502,8607,8708,8812,8917,9002,9097,9202,9311,9401,9501,9599,9710,9826,9926,10017,10091,10181,10270,10362,10445,10527,10616,10696,10778,10875,10969,11062,11155,11239,11336,11432,11527,11635,11715,11809", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "6468,6571,6675,6777,6869,6957,7061,7166,7271,7387,7469,7565,7649,7737,7842,7955,8056,8165,8272,8380,8497,8602,8703,8807,8912,8997,9092,9197,9306,9396,9496,9594,9705,9821,9921,10012,10086,10176,10265,10357,10440,10522,10611,10691,10773,10870,10964,11057,11150,11234,11331,11427,11522,11630,11710,11804,11896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a6bb74210ae1d7265ae291f685894631\\transformed\\material-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "11901", "endColumns": "84", "endOffsets": "11981"}}]}]}