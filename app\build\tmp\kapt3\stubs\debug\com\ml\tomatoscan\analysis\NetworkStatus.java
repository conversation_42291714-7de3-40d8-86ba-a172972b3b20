package com.ml.tomatoscan.analysis;

/**
 * Network status data class
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0007J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\'\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00032\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\nR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\n\u00a8\u0006\u0015"}, d2 = {"Lcom/ml/tomatoscan/analysis/NetworkStatus;", "", "isConnected", "", "connectionType", "Lcom/ml/tomatoscan/network/ConnectionType;", "isSuitableForHeavyOperations", "(ZLcom/ml/tomatoscan/network/ConnectionType;Z)V", "getConnectionType", "()Lcom/ml/tomatoscan/network/ConnectionType;", "()Z", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class NetworkStatus {
    private final boolean isConnected = false;
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.network.ConnectionType connectionType = null;
    private final boolean isSuitableForHeavyOperations = false;
    
    public NetworkStatus(boolean isConnected, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.network.ConnectionType connectionType, boolean isSuitableForHeavyOperations) {
        super();
    }
    
    public final boolean isConnected() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.ml.tomatoscan.network.ConnectionType getConnectionType() {
        return null;
    }
    
    public final boolean isSuitableForHeavyOperations() {
        return false;
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.ml.tomatoscan.network.ConnectionType component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.ml.tomatoscan.analysis.NetworkStatus copy(boolean isConnected, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.network.ConnectionType connectionType, boolean isSuitableForHeavyOperations) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}