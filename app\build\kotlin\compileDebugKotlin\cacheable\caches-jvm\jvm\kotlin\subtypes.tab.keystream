#androidx.activity.ComponentActivityandroid.app.Application+androidx.camera.core.CameraXConfig.Providerandroidx.room.RoomDatabaseandroid.os.Parcelable"androidx.compose.ui.graphics.Shape-com.ml.tomatoscan.ui.navigation.BottomNavItemkotlin.Enum)java.lang.Thread.UncaughtExceptionHandlercoil.fetch.Fetchercoil.fetch.Fetcher.Factory#androidx.lifecycle.AndroidViewModelandroidx.lifecycle.ViewModel,androidx.lifecycle.ViewModelProvider.Factory                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    