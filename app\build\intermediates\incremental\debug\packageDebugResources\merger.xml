<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res"><file name="app_logo" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\app_logo.xml" qualifiers="" type="drawable"/><file name="disease_bacterial_spot" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\disease_bacterial_spot.jpg" qualifiers="" type="drawable"/><file name="disease_early_blight" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\disease_early_blight.jpeg" qualifiers="" type="drawable"/><file name="disease_fusarium_wilt" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\disease_fusarium_wilt.jpeg" qualifiers="" type="drawable"/><file name="disease_late_blight" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\disease_late_blight.jpeg" qualifiers="" type="drawable"/><file name="disease_mosaic_virus" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\disease_mosaic_virus.jpeg" qualifiers="" type="drawable"/><file name="disease_septoria_leaf_spot" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\disease_septoria_leaf_spot.jpeg" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="scan_icon" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\scan_icon.xml" qualifiers="" type="drawable"/><file name="tomato_leaf" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\drawable\tomato_leaf.xml" qualifiers="" type="drawable"/><file name="app_logo" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-anydpi-v26\app_logo.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="app_logo_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-anydpi-v26\app_logo_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="app_logo" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-hdpi\app_logo.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="app_logo_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-hdpi\app_logo_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="app_logo" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-mdpi\app_logo.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="app_logo_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-mdpi\app_logo_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="app_logo" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xhdpi\app_logo.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="app_logo_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xhdpi\app_logo_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="app_logo" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxhdpi\app_logo.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="app_logo_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxhdpi\app_logo_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="app_logo" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxxhdpi\app_logo.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="app_logo_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxxhdpi\app_logo_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\values\app_logo.xml" qualifiers=""><color name="app_logo_background">#FFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#EE6331</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">TomatoScan</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.TomatoScan" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">893241113767</string><string name="google_api_key" translatable="false">AIzaSyAg6HfOSv3IkpD68FDUJaSv8MeDqvO35SA</string><string name="google_app_id" translatable="false">1:893241113767:android:d8a6d03a11278140572f82</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyAg6HfOSv3IkpD68FDUJaSv8MeDqvO35SA</string><string name="google_storage_bucket" translatable="false">tomatoscan.firebasestorage.app</string><string name="project_id" translatable="false">tomatoscan</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>