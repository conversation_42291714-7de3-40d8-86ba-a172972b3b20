{"logs": [{"outputFile": "com.ml.tomatoscan.app-mergeDebugResources-80:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab6cbaf7bf230b6b6d5af555fdbb71dd\\transformed\\browser-1.4.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "59,63,64,65", "startColumns": "4,4,4,4", "startOffsets": "6346,6743,6842,6954", "endColumns": "114,98,111,105", "endOffsets": "6456,6837,6949,7055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\827848161c01d8e105936428022348b0\\transformed\\material3-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4673,4763,4852,4953,5033,5117,5218,5324,5416,5515,5603,5715,5816,5920,6039,6119,6219", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4668,4758,4847,4948,5028,5112,5213,5319,5411,5510,5598,5710,5811,5915,6034,6114,6214,6306"}, "to": {"startLines": "68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7240,7359,7480,7596,7712,7814,7911,8025,8159,8277,8429,8513,8614,8709,8809,8924,9054,9160,9299,9435,9566,9732,9859,9979,10103,10223,10319,10416,10536,10652,10752,10863,10972,11112,11257,11367,11470,11556,11650,11742,11858,11948,12037,12138,12218,12302,12403,12509,12601,12700,12788,12900,13001,13105,13224,13304,13404", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "7354,7475,7591,7707,7809,7906,8020,8154,8272,8424,8508,8609,8704,8804,8919,9049,9155,9294,9430,9561,9727,9854,9974,10098,10218,10314,10411,10531,10647,10747,10858,10967,11107,11252,11362,11465,11551,11645,11737,11853,11943,12032,12133,12213,12297,12398,12504,12596,12695,12783,12895,12996,13100,13219,13299,13399,13491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d934593cab4de80219eb571e0fe7548b\\transformed\\play-services-basement-18.3.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5096", "endColumns": "144", "endOffsets": "5236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5468497be68ad4ba67415839697dae57\\transformed\\credentials-1.2.0-rc01\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2843,2952", "endColumns": "108,121", "endOffsets": "2947,3069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a6bb74210ae1d7265ae291f685894631\\transformed\\material-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "13496", "endColumns": "88", "endOffsets": "13580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\398c2a74b03599659b5ead5e2fab5e05\\transformed\\ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "197,292,378,475,574,660,743,840,931,1018,1103,1193,1269,1354,1430,1509,1584,1660,1727", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "287,373,470,569,655,738,835,926,1013,1098,1188,1264,1349,1425,1504,1579,1655,1722,1835"}, "to": {"startLines": "39,40,60,61,62,66,67,126,127,128,129,131,132,133,134,135,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3903,3998,6461,6558,6657,7060,7143,13585,13676,13763,13848,14024,14100,14185,14261,14340,14516,14592,14659", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "3993,4079,6553,6652,6738,7138,7235,13671,13758,13843,13933,14095,14180,14256,14335,14410,14587,14654,14767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3d5cb58bacb9a0b03b60afe3e3ab7d36\\transformed\\appcompat-1.6.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,13938", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,14019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aef2b099c308afe7b74c31a0cefc610e\\transformed\\foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,146,229", "endColumns": "90,82,84", "endOffsets": "141,224,309"}, "to": {"startLines": "31,140,141", "startColumns": "4,4,4", "startOffsets": "3074,14772,14855", "endColumns": "90,82,84", "endOffsets": "3160,14850,14935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e33c8e88ddf3587b7d540033450542b0\\transformed\\play-services-base-18.1.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4084,4189,4337,4464,4572,4739,4869,4991,5241,5411,5519,5683,5813,5970,6127,6196,6262", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "4184,4332,4459,4567,4734,4864,4986,5091,5406,5514,5678,5808,5965,6122,6191,6257,6341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf21ceb9549d3cbcfc1e34a7f2650da\\transformed\\core-1.16.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "32,33,34,35,36,37,38,136", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3165,3262,3364,3463,3563,3673,3783,14415", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "3257,3359,3458,3558,3668,3778,3898,14511"}}]}]}