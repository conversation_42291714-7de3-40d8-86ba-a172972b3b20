{"logs": [{"outputFile": "com.ml.tomatoscan.app-mergeDebugResources-80:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf21ceb9549d3cbcfc1e34a7f2650da\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "9,28,29,48,49,81,82,192,193,194,195,196,197,198,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,280,281,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,321,354,355,356,357,358,359,360,468,1857,1858,1862,1863,1867,2016,2017,2670,2704,2760,2795,2825,2858", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1477,1549,2942,3007,5154,5223,12557,12627,12695,12767,12837,12898,12972,13829,13890,13951,14013,14077,14139,14200,14268,14368,14428,14494,14567,14636,14693,14745,15260,15332,15408,15473,15532,15591,15651,15711,15771,15831,15891,15951,16011,16071,16131,16191,16250,16310,16370,16430,16490,16550,16610,16670,16730,16790,16850,16909,16969,17029,17088,17147,17206,17265,17324,18142,18177,18581,18636,18699,18754,18812,18868,18926,18987,19050,19107,19158,19216,19266,19327,19384,19450,19484,19519,20337,22616,22683,22755,22824,22893,22967,23039,31787,123816,123933,124134,124244,124445,136179,136251,157751,159324,161554,163360,164360,165042", "endLines": "9,28,29,48,49,81,82,192,193,194,195,196,197,198,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,280,281,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,321,354,355,356,357,358,359,360,468,1857,1861,1862,1866,1867,2016,2017,2675,2713,2794,2815,2857,2863", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1544,1632,3002,3068,5218,5281,12622,12690,12762,12832,12893,12967,13040,13885,13946,14008,14072,14134,14195,14263,14363,14423,14489,14562,14631,14688,14740,14802,15327,15403,15468,15527,15586,15646,15706,15766,15826,15886,15946,16006,16066,16126,16186,16245,16305,16365,16425,16485,16545,16605,16665,16725,16785,16845,16904,16964,17024,17083,17142,17201,17260,17319,17378,18172,18207,18631,18694,18749,18807,18863,18921,18982,19045,19102,19153,19211,19261,19322,19379,19445,19479,19514,19549,20402,22678,22750,22819,22888,22962,23034,23122,31853,123928,124129,124239,124440,124569,136246,136313,157949,159620,163355,164036,165037,165204"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TomatoScan\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "293,57,105,153,201,247,336", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "330,99,147,195,241,287,373"}, "to": {"startLines": "35,91,92,93,104,105,108", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "2010,5874,5921,5968,6712,6757,6923", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2047,5916,5963,6010,6752,6797,6960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7eb931d2788947f0022d909f1391480f\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "311", "startColumns": "4", "startOffsets": "19732", "endColumns": "53", "endOffsets": "19781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b9eb6595b88cb5a94e9e9a83ebd14d7f\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "19629", "endColumns": "42", "endOffsets": "19667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3d5cb58bacb9a0b03b60afe3e3ab7d36\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,31,32,33,34,36,37,38,39,40,41,46,47,58,59,60,61,62,63,64,65,66,67,69,70,71,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,106,107,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,199,200,201,202,203,204,205,206,207,223,224,225,226,227,228,229,230,266,267,268,269,277,284,285,288,307,315,316,317,318,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,462,474,475,476,477,478,479,487,488,492,496,500,505,511,518,522,526,531,535,539,543,547,551,555,561,565,571,575,581,585,590,594,597,601,607,611,617,621,627,630,634,638,642,646,650,651,652,653,656,659,662,665,669,670,671,672,673,676,678,680,682,687,688,692,698,702,703,705,717,718,722,728,732,733,734,738,765,769,770,774,802,974,1000,1171,1197,1228,1236,1242,1258,1280,1285,1290,1300,1309,1318,1322,1329,1348,1355,1356,1365,1368,1371,1375,1379,1383,1386,1387,1392,1397,1407,1412,1419,1425,1426,1429,1433,1438,1440,1442,1445,1448,1450,1454,1457,1464,1467,1470,1474,1476,1480,1482,1484,1486,1490,1498,1506,1518,1524,1533,1536,1547,1550,1551,1556,1557,1586,1655,1725,1726,1736,1745,1746,1748,1752,1755,1758,1761,1764,1767,1770,1773,1777,1780,1783,1786,1790,1793,1797,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1823,1825,1826,1827,1828,1829,1830,1831,1832,1834,1835,1837,1838,1840,1842,1843,1845,1846,1847,1848,1849,1850,1852,1853,1854,1855,1856,1868,1870,1872,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1888,1889,1890,1891,1892,1893,1894,1896,1900,1909,1910,1911,1912,1913,1914,1918,1919,1920,1921,1923,1925,1927,1929,1931,1932,1933,1934,1936,1938,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1954,1955,1956,1957,1959,1961,1962,1964,1965,1967,1969,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1984,1985,1986,1987,1989,1990,1991,1992,1993,1995,1997,1999,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2018,2093,2096,2099,2102,2116,2129,2171,2174,2203,2230,2239,2303,2666,2676,2714,2742,2864,2888,2894,2913,2934,3058,3117,3123,3131,3137,3191,3223,3289,3309,3364,3376,3402", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,976,1040,1110,1171,1246,1322,1399,1691,1776,1858,1934,2052,2129,2207,2313,2419,2498,2827,2884,3744,3818,3893,3958,4024,4084,4145,4217,4290,4357,4482,4541,4600,4659,4718,4777,4831,4885,4938,4992,5046,5100,5286,5360,5439,5512,5586,5657,5729,5801,6015,6072,6130,6203,6277,6351,6426,6498,6571,6641,6802,6862,6965,7034,7103,7173,7247,7323,7387,7464,7540,7617,7682,7751,7828,7903,7972,8040,8117,8183,8244,8341,8406,8475,8574,8645,8704,8762,8819,8878,8942,9013,9085,9157,9229,9301,9368,9436,9504,9563,9626,9690,9780,9871,9931,9997,10064,10130,10200,10264,10317,10384,10445,10512,10625,10683,10746,10811,10876,10951,11024,11096,11140,11187,11233,11282,11343,11404,11465,11527,11591,11655,11719,11784,11847,11907,11968,12034,12093,12153,12215,12286,12346,13045,13131,13218,13308,13395,13483,13565,13648,13738,14807,14859,14917,14962,15028,15092,15149,15206,17383,17440,17488,17537,17999,18332,18379,18535,19554,19953,20017,20079,20139,20407,20481,20551,20629,20683,20753,20838,20886,20932,20993,21056,21122,21186,21257,21320,21385,21449,21510,21571,21623,21696,21770,21839,21914,21988,22062,22203,31506,32142,32220,32310,32398,32494,32584,33166,33255,33502,33783,34035,34320,34713,35190,35412,35634,35910,36137,36367,36597,36827,37057,37284,37703,37929,38354,38584,39012,39231,39514,39722,39853,40080,40506,40731,41158,41379,41804,41924,42200,42501,42825,43116,43430,43567,43698,43803,44045,44212,44416,44624,44895,45007,45119,45224,45341,45555,45701,45841,45927,46275,46363,46609,47027,47276,47358,47456,48113,48213,48465,48889,49144,49238,49327,49564,51588,51830,51932,52185,54341,65022,66538,77233,78761,80518,81144,81564,82825,84090,84346,84582,85129,85623,86228,86426,87006,88374,88749,88867,89405,89562,89758,90031,90287,90457,90598,90662,91027,91394,92070,92334,92672,93025,93119,93305,93611,93873,93998,94125,94364,94575,94694,94887,95064,95519,95700,95822,96081,96194,96381,96483,96590,96719,96994,97502,97998,98875,99169,99739,99888,100620,100792,100876,101212,101304,102930,108161,113532,113594,114172,114756,114847,114960,115189,115349,115501,115672,115838,116007,116174,116337,116580,116750,116923,117094,117368,117567,117772,118102,118186,118282,118378,118476,118576,118678,118780,118882,118984,119086,119186,119282,119394,119523,119646,119777,119908,120006,120120,120214,120354,120488,120584,120696,120796,120912,121008,121120,121220,121360,121496,121660,121790,121948,122098,122239,122383,122518,122630,122780,122908,123036,123172,123304,123434,123564,123676,124574,124720,124864,125002,125068,125158,125234,125338,125428,125530,125638,125746,125846,125926,126018,126116,126226,126278,126356,126462,126554,126658,126768,126890,127053,127520,127600,127700,127790,127900,127990,128231,128325,128431,128523,128623,128735,128849,128965,129081,129175,129289,129401,129503,129623,129745,129827,129931,130051,130177,130275,130369,130457,130569,130685,130807,130919,131094,131210,131296,131388,131500,131624,131691,131817,131885,132013,132157,132285,132354,132449,132564,132677,132776,132885,132996,133107,133208,133313,133413,133543,133634,133757,133851,133963,134049,134153,134249,134337,134455,134559,134663,134789,134877,134985,135085,135175,135285,135369,135471,135555,135609,135673,135779,135865,135975,136059,136318,138934,139052,139167,139247,139608,140145,141549,141627,142971,144332,144720,147563,157616,157954,159625,160982,165209,165960,166222,166737,167116,171394,173675,173904,174198,174413,175913,176763,179789,180533,182664,183004,184315", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,31,32,33,34,36,37,38,39,40,41,46,47,58,59,60,61,62,63,64,65,66,67,69,70,71,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,106,107,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,199,200,201,202,203,204,205,206,207,223,224,225,226,227,228,229,230,266,267,268,269,277,284,285,288,307,315,316,317,318,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,462,474,475,476,477,478,486,487,491,495,499,504,510,517,521,525,530,534,538,542,546,550,554,560,564,570,574,580,584,589,593,596,600,606,610,616,620,626,629,633,637,641,645,649,650,651,652,655,658,661,664,668,669,670,671,672,675,677,679,681,686,687,691,697,701,702,704,716,717,721,727,731,732,733,737,764,768,769,773,801,973,999,1170,1196,1227,1235,1241,1257,1279,1284,1289,1299,1308,1317,1321,1328,1347,1354,1355,1364,1367,1370,1374,1378,1382,1385,1386,1391,1396,1406,1411,1418,1424,1425,1428,1432,1437,1439,1441,1444,1447,1449,1453,1456,1463,1466,1469,1473,1475,1479,1481,1483,1485,1489,1497,1505,1517,1523,1532,1535,1546,1549,1550,1555,1556,1561,1654,1724,1725,1735,1744,1745,1747,1751,1754,1757,1760,1763,1766,1769,1772,1776,1779,1782,1785,1789,1792,1796,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1822,1824,1825,1826,1827,1828,1829,1830,1831,1833,1834,1836,1837,1839,1841,1842,1844,1845,1846,1847,1848,1849,1851,1852,1853,1854,1855,1856,1869,1871,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1887,1888,1889,1890,1891,1892,1893,1895,1899,1903,1909,1910,1911,1912,1913,1917,1918,1919,1920,1922,1924,1926,1928,1930,1931,1932,1933,1935,1937,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1953,1954,1955,1956,1958,1960,1961,1963,1964,1966,1968,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1983,1984,1985,1986,1988,1989,1990,1991,1992,1994,1996,1998,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2092,2095,2098,2101,2115,2121,2138,2173,2202,2229,2238,2302,2665,2669,2703,2741,2759,2887,2893,2899,2933,3057,3077,3122,3126,3136,3171,3202,3288,3308,3363,3375,3401,3408", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,971,1035,1105,1166,1241,1317,1394,1472,1771,1853,1929,2005,2124,2202,2308,2414,2493,2573,2879,2937,3813,3888,3953,4019,4079,4140,4212,4285,4352,4420,4536,4595,4654,4713,4772,4826,4880,4933,4987,5041,5095,5149,5355,5434,5507,5581,5652,5724,5796,5869,6067,6125,6198,6272,6346,6421,6493,6566,6636,6707,6857,6918,7029,7098,7168,7242,7318,7382,7459,7535,7612,7677,7746,7823,7898,7967,8035,8112,8178,8239,8336,8401,8470,8569,8640,8699,8757,8814,8873,8937,9008,9080,9152,9224,9296,9363,9431,9499,9558,9621,9685,9775,9866,9926,9992,10059,10125,10195,10259,10312,10379,10440,10507,10620,10678,10741,10806,10871,10946,11019,11091,11135,11182,11228,11277,11338,11399,11460,11522,11586,11650,11714,11779,11842,11902,11963,12029,12088,12148,12210,12281,12341,12409,13126,13213,13303,13390,13478,13560,13643,13733,13824,14854,14912,14957,15023,15087,15144,15201,15255,17435,17483,17532,17583,18028,18374,18423,18576,19581,20012,20074,20134,20191,20476,20546,20624,20678,20748,20833,20881,20927,20988,21051,21117,21181,21252,21315,21380,21444,21505,21566,21618,21691,21765,21834,21909,21983,22057,22198,22268,31554,32215,32305,32393,32489,32579,33161,33250,33497,33778,34030,34315,34708,35185,35407,35629,35905,36132,36362,36592,36822,37052,37279,37698,37924,38349,38579,39007,39226,39509,39717,39848,40075,40501,40726,41153,41374,41799,41919,42195,42496,42820,43111,43425,43562,43693,43798,44040,44207,44411,44619,44890,45002,45114,45219,45336,45550,45696,45836,45922,46270,46358,46604,47022,47271,47353,47451,48108,48208,48460,48884,49139,49233,49322,49559,51583,51825,51927,52180,54336,65017,66533,77228,78756,80513,81139,81559,82820,84085,84341,84577,85124,85618,86223,86421,87001,88369,88744,88862,89400,89557,89753,90026,90282,90452,90593,90657,91022,91389,92065,92329,92667,93020,93114,93300,93606,93868,93993,94120,94359,94570,94689,94882,95059,95514,95695,95817,96076,96189,96376,96478,96585,96714,96989,97497,97993,98870,99164,99734,99883,100615,100787,100871,101207,101299,101577,108156,113527,113589,114167,114751,114842,114955,115184,115344,115496,115667,115833,116002,116169,116332,116575,116745,116918,117089,117363,117562,117767,118097,118181,118277,118373,118471,118571,118673,118775,118877,118979,119081,119181,119277,119389,119518,119641,119772,119903,120001,120115,120209,120349,120483,120579,120691,120791,120907,121003,121115,121215,121355,121491,121655,121785,121943,122093,122234,122378,122513,122625,122775,122903,123031,123167,123299,123429,123559,123671,123811,124715,124859,124997,125063,125153,125229,125333,125423,125525,125633,125741,125841,125921,126013,126111,126221,126273,126351,126457,126549,126653,126763,126885,127048,127205,127595,127695,127785,127895,127985,128226,128320,128426,128518,128618,128730,128844,128960,129076,129170,129284,129396,129498,129618,129740,129822,129926,130046,130172,130270,130364,130452,130564,130680,130802,130914,131089,131205,131291,131383,131495,131619,131686,131812,131880,132008,132152,132280,132349,132444,132559,132672,132771,132880,132991,133102,133203,133308,133408,133538,133629,133752,133846,133958,134044,134148,134244,134332,134450,134554,134658,134784,134872,134980,135080,135170,135280,135364,135466,135550,135604,135668,135774,135860,135970,136054,136174,138929,139047,139162,139242,139603,139836,140657,141622,142966,144327,144715,147558,157611,157746,159319,160977,161549,165955,166217,166417,167111,171389,171995,173899,174050,174408,175491,176220,179784,180528,182659,182999,184310,184513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2540ec706e630cd63a630498acd97ae8\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "275", "startColumns": "4", "startOffsets": "17882", "endColumns": "65", "endOffsets": "17943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4f04e197689bb26cdcc3b07cde404826\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "308", "startColumns": "4", "startOffsets": "19586", "endColumns": "42", "endOffsets": "19624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\827848161c01d8e105936428022348b0\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "320,395,396,397,398,399,400,401,402,403,404,407,408,409,410,411,412,413,414,415,416,417,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,1565,1575", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20264,26716,26804,26890,26971,27055,27124,27189,27272,27378,27464,27584,27638,27707,27768,27837,27926,28021,28095,28192,28285,28383,28532,28623,28711,28807,28905,28969,29037,29124,29218,29285,29357,29429,29530,29639,29715,29784,29832,29898,29962,30036,30093,30150,30222,30272,30326,30397,30468,30538,30607,30665,30741,30812,30886,30972,31022,31092,101694,102409", "endLines": "320,395,396,397,398,399,400,401,402,403,406,407,408,409,410,411,412,413,414,415,416,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,1574,1577", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "20332,26799,26885,26966,27050,27119,27184,27267,27373,27459,27579,27633,27702,27763,27832,27921,28016,28090,28187,28280,28378,28527,28618,28706,28802,28900,28964,29032,29119,29213,29280,29352,29424,29525,29634,29710,29779,29827,29893,29957,30031,30088,30145,30217,30267,30321,30392,30463,30533,30602,30660,30736,30807,30881,30967,31017,31087,31152,102404,102557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bac82bfe1b55c5297ccfca56a4d83e86\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3078,3091,3097,3103,3112", "startColumns": "4,4,4,4,4", "startOffsets": "172000,172639,172883,173130,173493", "endLines": "3090,3096,3102,3105,3116", "endColumns": "24,24,24,24,24", "endOffsets": "172634,172878,173125,173258,173670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d934593cab4de80219eb571e0fe7548b\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "319,371", "startColumns": "4,4", "startOffsets": "20196,24286", "endColumns": "67,166", "endOffsets": "20259,24448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5468497be68ad4ba67415839697dae57\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "349,350", "startColumns": "4,4", "startOffsets": "22273,22355", "endColumns": "81,83", "endOffsets": "22350,22434"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TomatoScan\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "59", "endColumns": "87", "endOffsets": "142"}, "to": {"startLines": "1908", "startColumns": "4", "startOffsets": "127433", "endColumns": "86", "endOffsets": "127515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\31688599dc18510b240530df420f5aab\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "312", "startColumns": "4", "startOffsets": "19786", "endColumns": "49", "endOffsets": "19831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4073bc2e6f6d89e3ea8d0133509ac240\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "274,287,313,2816,2821", "startColumns": "4,4,4,4,4", "startOffsets": "17825,18470,19836,164041,164211", "endLines": "274,287,313,2820,2824", "endColumns": "56,64,63,24,24", "endOffsets": "17877,18530,19895,164206,164355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab6cbaf7bf230b6b6d5af555fdbb71dd\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "42,43,44,45,190,191,381,385,386,387", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2578,2636,2702,2765,12414,12485,25579,25872,25939,26018", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2631,2697,2760,2822,12480,12552,25642,25934,26013,26082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e33c8e88ddf3587b7d540033450542b0\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "50,51,52,53,54,55,56,57,363,364,365,366,367,368,369,370,372,373,374,375,376,377,378,379,380,2900,3172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3073,3163,3243,3333,3423,3503,3584,3664,23246,23351,23532,23657,23764,23944,24067,24183,24453,24641,24746,24927,25052,25227,25375,25438,25500,166422,175496", "endLines": "50,51,52,53,54,55,56,57,363,364,365,366,367,368,369,370,372,373,374,375,376,377,378,379,380,2912,3190", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "3158,3238,3328,3418,3498,3579,3659,3739,23346,23527,23652,23759,23939,24062,24178,24281,24636,24741,24922,25047,25222,25370,25433,25495,25574,166732,175908"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TomatoScan\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "56", "endOffsets": "109"}, "to": {"startLines": "68", "startColumns": "4", "startOffsets": "4425", "endColumns": "56", "endOffsets": "4477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\398c2a74b03599659b5ead5e2fab5e05\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "270,272,273,276,278,314,361,362,382,383,384,393,394,457,458,460,461,463,464,465,466,467,469,470,471,1562,1578,1581", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17588,17712,17770,17948,18033,19900,23127,23192,25647,25713,25814,26604,26656,31215,31277,31402,31452,31559,31605,31659,31705,31747,31858,31905,31941,101582,102562,102673", "endLines": "270,272,273,276,278,314,361,362,382,383,384,393,394,457,458,460,461,463,464,465,466,467,469,470,471,1564,1580,1585", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "17657,17765,17820,17994,18083,19948,23187,23241,25708,25809,25867,26651,26711,31272,31326,31447,31501,31600,31654,31700,31742,31782,31900,31936,32026,101689,102668,102925"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TomatoScan\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,572", "endColumns": "81,103,108,119,101,70", "endOffsets": "132,236,345,465,567,638"}, "to": {"startLines": "388,389,390,391,392,459", "startColumns": "4,4,4,4,4,4", "startOffsets": "26087,26169,26273,26382,26502,31331", "endColumns": "81,103,108,119,101,70", "endOffsets": "26164,26268,26377,26497,26599,31397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a6bb74210ae1d7265ae291f685894631\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "456", "startColumns": "4", "startOffsets": "31157", "endColumns": "57", "endOffsets": "31210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0a2c29bba24857d7c570746c5786e70\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "279,283", "startColumns": "4,4", "startOffsets": "18088,18265", "endColumns": "53,66", "endOffsets": "18137,18327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d712c985431b5e7aef45e80b23bf8468\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "282,2122,3106,3109", "startColumns": "4,4,4,4", "startOffsets": "18212,139841,173263,173378", "endLines": "282,2128,3108,3111", "endColumns": "52,24,24,24", "endOffsets": "18260,140140,173373,173488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ecdc65225691c83e9d9f77d6b8cb8da\\transformed\\coil-base-2.6.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "271", "startColumns": "4", "startOffsets": "17662", "endColumns": "49", "endOffsets": "17707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\815fcbe92cd40c74aa601a86cff0c471\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "351", "startColumns": "4", "startOffsets": "22439", "endColumns": "82", "endOffsets": "22517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\29703a8d71786ba318cd640639b080bf\\transformed\\camera-view-1.3.4\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,3127", "startColumns": "4,4,4", "startOffsets": "250,511,174055", "endLines": "7,17,3130", "endColumns": "11,11,24", "endOffsets": "397,813,174193"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TomatoScan\\app\\src\\main\\res\\values\\app_logo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "53", "endOffsets": "106"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "1637", "endColumns": "53", "endOffsets": "1686"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TomatoScan\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "17", "endColumns": "47", "endOffsets": "60"}, "to": {"startLines": "352", "startColumns": "4", "startOffsets": "22522", "endColumns": "47", "endOffsets": "22565"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb3ae901071214bde197020db9c1aa8c\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2139,2155,2161,3203,3219", "startColumns": "4,4,4,4,4", "startOffsets": "140662,141087,141265,176225,176636", "endLines": "2154,2160,2170,3218,3222", "endColumns": "24,24,24,24,24", "endOffsets": "141082,141260,141544,176631,176758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aef2b099c308afe7b74c31a0cefc610e\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "353,472,473", "startColumns": "4,4,4", "startOffsets": "22570,32031,32087", "endColumns": "45,55,54", "endOffsets": "22611,32082,32137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b862771fe750953b0a80430122a3472f\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "286,310", "startColumns": "4,4", "startOffsets": "18428,19672", "endColumns": "41,59", "endOffsets": "18465,19727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ceacb3293e83182893e1276d9fbc1ffe\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "1904", "startColumns": "4", "startOffsets": "127210", "endLines": "1907", "endColumns": "12", "endOffsets": "127428"}}]}]}