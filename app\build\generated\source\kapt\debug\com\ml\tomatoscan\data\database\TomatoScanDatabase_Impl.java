package com.ml.tomatoscan.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.ml.tomatoscan.data.database.dao.AnalysisDao;
import com.ml.tomatoscan.data.database.dao.AnalysisDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class TomatoScanDatabase_Impl extends TomatoScanDatabase {
  private volatile AnalysisDao _analysisDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(1) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `analysis_results` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `imageData` BLOB NOT NULL, `imagePath` TEXT, `diseaseDetected` TEXT NOT NULL, `severity` TEXT NOT NULL, `confidence` REAL NOT NULL, `description` TEXT NOT NULL, `recommendations` TEXT NOT NULL, `treatmentOptions` TEXT NOT NULL, `preventionMeasures` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `quality` TEXT NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '03c19cdf0c2476adb8d9a3a3f1188a3c')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `analysis_results`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsAnalysisResults = new HashMap<String, TableInfo.Column>(12);
        _columnsAnalysisResults.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("imageData", new TableInfo.Column("imageData", "BLOB", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("imagePath", new TableInfo.Column("imagePath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("diseaseDetected", new TableInfo.Column("diseaseDetected", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("severity", new TableInfo.Column("severity", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("confidence", new TableInfo.Column("confidence", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("recommendations", new TableInfo.Column("recommendations", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("treatmentOptions", new TableInfo.Column("treatmentOptions", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("preventionMeasures", new TableInfo.Column("preventionMeasures", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAnalysisResults.put("quality", new TableInfo.Column("quality", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAnalysisResults = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAnalysisResults = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoAnalysisResults = new TableInfo("analysis_results", _columnsAnalysisResults, _foreignKeysAnalysisResults, _indicesAnalysisResults);
        final TableInfo _existingAnalysisResults = TableInfo.read(db, "analysis_results");
        if (!_infoAnalysisResults.equals(_existingAnalysisResults)) {
          return new RoomOpenHelper.ValidationResult(false, "analysis_results(com.ml.tomatoscan.data.database.entities.AnalysisEntity).\n"
                  + " Expected:\n" + _infoAnalysisResults + "\n"
                  + " Found:\n" + _existingAnalysisResults);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "03c19cdf0c2476adb8d9a3a3f1188a3c", "5eda077835b48c9d322fa22b3e7f538b");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "analysis_results");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `analysis_results`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(AnalysisDao.class, AnalysisDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public AnalysisDao analysisDao() {
    if (_analysisDao != null) {
      return _analysisDao;
    } else {
      synchronized(this) {
        if(_analysisDao == null) {
          _analysisDao = new AnalysisDao_Impl(this);
        }
        return _analysisDao;
      }
    }
  }
}
